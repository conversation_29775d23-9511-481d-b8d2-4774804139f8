"use client";

import Link from "next/link";
import React, { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { Heart, SearchIcon, ShoppingBag, Settings, LogOut, Shield } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { User, UserRole } from "@/utils/types";
import { usePathname } from "next/navigation";
import { signOut } from "@/lib/auth-client";
import { Badge } from "@/components/ui/badge";
import { useCart } from "@/contexts/cart-context";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import MiniCart from "./cart/mini-cart";

type Props = {
  user: User;
};

const NavBar = ({ user }: Props) => {
  const pathname = usePathname();
  const { state: cartState } = useCart();
  const [cartOpen, setCartOpen] = useState(false);

  return (
    <div className="w-full h-16 flex items-center justify-between p-2 border-b">
      <div className="h-full flex items-center justify-center">
        <h1 className="text-2xl font-bold">Rivv</h1>
      </div>
      <div className="h-full flex items-center gap-4  w-[50%]">
        <nav className="flex space-x-6 ">
          <Link href="/dashboard" className={`text-lg ${pathname === "/dashboard" ? "underline" : ""}`}>
            Dashboard
          </Link>
          <Link href="/products" className={`text-lg ${pathname === "/products" ? "font-semibold underline" : ""}`}>
            Products
          </Link>
          <Link href="/orders" className={`text-lg ${pathname === "/orders" ? "font-semibold underline" : ""}`}>
            Orders
          </Link>
          {user.role === UserRole.ADMIN && (
            <Link href="/admin" className={`text-lg ${pathname.startsWith("/admin") ? "font-semibold underline" : ""}`}>
              <div className="flex items-center gap-1">
                <Shield className="h-4 w-4" />
                Admin
              </div>
            </Link>
          )}
        </nav>
        <Link
          href={"/products"}
          className=" rounded-md bg-gray-200 w-[50%] flex items-center p-2"
        >
          <SearchIcon className="text-gray-500 h-5 w-5 border mr-2" />
          <span className="w-full">Search</span>
        </Link>
      </div>
      <div className="flex gap-2">
        <Popover open={cartOpen} onOpenChange={setCartOpen}>
          <PopoverTrigger asChild>
            <button className="flex items-center flex-col relative hover:cursor-pointer">
              <div className="relative">
                <ShoppingBag className="h-5 w-5 text-gray-500 hover:text-green-500 transition-colors duration-200" />
                {cartState.totalItems > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
                  >
                    {cartState.totalItems > 99 ? "99+" : cartState.totalItems}
                  </Badge>
                )}
              </div>
              <span className="text-sm text-muted-foreground">Cart</span>
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <MiniCart onClose={() => setCartOpen(false)} />
          </PopoverContent>
        </Popover>
        <Link href={"/favourites"} className="flex items-center flex-col">
          <Heart className="h-5 w-5 text-gray-500 hover:text-red-500 transition-colors duration-200" />
          <span className="text-sm text-muted-foreground">Favourites</span>
        </Link>

        <DropdownMenu>
          <DropdownMenuTrigger>
            <div className="flex items-center gap-2">
              <Avatar className="hover:cursor-pointer">
                <AvatarImage src={user.image || ""} />
                <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
              </Avatar>
              {user.role === UserRole.ADMIN && (
                <Badge variant="secondary" className="text-xs">
                  Admin
                </Badge>
              )}
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{user.name}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center">
                <Settings className="mr-2 h-4 w-4" />
                Profile Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/orders" className="flex items-center">
                <ShoppingBag className="mr-2 h-4 w-4" />
                My Orders
              </Link>
            </DropdownMenuItem>
            {user.role === UserRole.ADMIN && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/admin" className="flex items-center">
                    <Shield className="mr-2 h-4 w-4" />
                    Admin Dashboard
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="flex items-center text-red-600 focus:text-red-600"
              onClick={async () => {
                await signOut({
                  fetchOptions: {
                    onSuccess: () => {
                      window.location.href = "/";
                    },
                  },
                });
              }}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default NavBar;
