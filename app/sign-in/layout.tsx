import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Sign In",
  description: "Access your RIVV account to experience quality that speaks for itself. Sign in to view your curated orders and discover premium footwear selected just for you.",
  keywords: [
    "sign in",
    "login",
    "premium account",
    "curated experience",
    "quality footwear",
    "secure login",
    "Google sign-in",
    "personalized shopping"
  ],
  path: "/sign-in",
  noIndex: true, // Auth pages should not be indexed
});

export default function SignInLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
