import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Sign In",
  description: "Sign in to your Rivv account to access your dashboard, view orders, and enjoy a personalized shopping experience. Secure authentication with Google sign-in.",
  keywords: [
    "sign in",
    "login",
    "user authentication",
    "account access",
    "secure login",
    "Google sign-in"
  ],
  path: "/sign-in",
  noIndex: true, // Auth pages should not be indexed
});

export default function SignInLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
