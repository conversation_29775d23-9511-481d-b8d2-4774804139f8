import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Shopping Cart",
  description: "Review your selected items before checkout. Manage quantities, apply discount codes, and proceed to secure payment. Free delivery available for qualifying orders.",
  keywords: [
    "shopping cart",
    "cart review",
    "order summary",
    "discount codes",
    "quantity management",
    "proceed to checkout"
  ],
  path: "/cart",
  noIndex: true, // Cart pages should not be indexed
});

export default function CartLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
