import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Orders",
  description: "View and track your orders at Rivv. Check order status, delivery information, and order history. Get updates on your purchases and delivery progress.",
  keywords: [
    "order tracking",
    "order history",
    "delivery status",
    "purchase history",
    "order management",
    "order details",
    "shipping information"
  ],
  path: "/orders",
  noIndex: true, // Order pages should not be indexed for privacy
});

export default function OrdersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
