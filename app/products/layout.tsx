import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Curated Collection",
  description: "Discover our purposefully curated collection of premium footwear. Every pair is carefully selected for its craftsmanship, comfort, and standout style. Quality that speaks for itself.",
  keywords: [
    "curated collection",
    "premium footwear",
    "quality craftsmanship",
    "standout style",
    "comfort",
    "carefully selected",
    "shoes online",
    "fashion accessories",
    "sneakers",
    "boots",
    "sandals",
    "purposefully curated"
  ],
  path: "/products",
});

export default function ProductsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
