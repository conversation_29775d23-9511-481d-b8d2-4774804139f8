import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Products",
  description: "Browse our extensive collection of premium footwear and fashion accessories. Filter by brand, category, price, and size. Find the perfect shoes and accessories with fast delivery across Lesotho.",
  keywords: [
    "product catalog",
    "footwear collection",
    "shoes online",
    "fashion accessories",
    "brand shoes",
    "sneakers",
    "boots",
    "sandals",
    "product search",
    "filter products"
  ],
  path: "/products",
});

export default function ProductsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
