import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Profile",
  description: "Manage your Rivv account profile. Update personal information, delivery addresses, and account preferences. Keep your account details current for the best shopping experience.",
  keywords: [
    "user profile",
    "account settings",
    "personal information",
    "delivery address",
    "account management",
    "profile settings"
  ],
  path: "/profile",
  noIndex: true, // Profile pages should not be indexed for privacy
});

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
