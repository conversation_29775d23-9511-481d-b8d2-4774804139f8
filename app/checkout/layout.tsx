import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Checkout",
  description: "Complete your purchase securely at Rivv. Multiple payment options including M-Pesa, EcoCash, and Bank Transfer. Free delivery in Maseru and for orders over M3500.",
  keywords: [
    "secure checkout",
    "payment options",
    "M-Pesa payment",
    "EcoCash payment",
    "bank transfer",
    "secure payment",
    "order completion",
    "delivery options",
    "Lesotho delivery"
  ],
  path: "/checkout",
  noIndex: true, // Checkout pages should not be indexed
});

export default function CheckoutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
