import { Metadata } from "next";
import { generatePageMetadata } from "@/lib/metadata";

export const metadata: Metadata = generatePageMetadata({
  title: "Secure Checkout",
  description: "Complete your RIVV purchase with confidence. Secure payment options including M-Pesa, EcoCash, and Bank Transfer. Premium quality delivered with care across Lesotho.",
  keywords: [
    "secure checkout",
    "premium purchase",
    "payment options",
    "M-Pesa payment",
    "EcoCash payment",
    "bank transfer",
    "secure payment",
    "quality delivery",
    "Lesotho delivery",
    "confidence"
  ],
  path: "/checkout",
  noIndex: true, // Checkout pages should not be indexed
});

export default function CheckoutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
