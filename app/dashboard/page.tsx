"use client";

import SpinnerCircle4 from "@/components/customized/spinner/spinner-10";
import Content from "@/components/dashboard/content";
import NavBar from "@/components/navbar";
import { UserRoute } from "@/components/auth/protected-route";
import { useSession } from "@/lib/auth-client";
import { User, UserRole } from "@/utils/types";
import React, { Suspense, useEffect } from "react";
import { getUserById } from "@/actions/userActions";

const DashboardPage = () => {
  return (
    <UserRoute>
      <DashboardContent />
    </UserRoute>
  );
};

const DashboardContent = () => {
  const { data } = useSession();
  const [role, setRole] = React.useState<UserRole | null>(null);

  // At this point, we know the user is authenticated due to UserRoute
  // Type cast to include the role property that exists at runtime
  const user = data!.user as User & { role?: UserRole };

  useEffect(() => {
    const getUserDetails = async () => {
      if (user) {
        const userFull = await getUserById(user.id);
        if (userFull.success && userFull.data) {
          setRole(userFull.data.role);
          user.role = userFull.data.role;
        }
      }
    };
    getUserDetails();
  }, [user]);

  return (
    <div className="w-full h-screen">
      <Suspense fallback={<SpinnerCircle4 />}>
        <NavBar user={user} />
      </Suspense>
      <Content user={user} />
    </div>
  );
};

export default DashboardPage;
