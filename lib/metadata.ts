import { Metadata } from "next";

// Base metadata configuration for the Rivv e-commerce platform
export const baseMetadata = {
  siteName: "RIVV",
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || "https://rivv.com",
  description: "RIVV is a proudly female-founded brand on a mission to deliver quality that speaks for itself. Every pair is carefully selected for its craftsmanship, comfort, and standout style. We're not here to meet expectations—we're here to exceed them. Step in with confidence. You won't be disappointed.",
  keywords: [
    "premium footwear",
    "female-founded brand",
    "quality craftsmanship",
    "standout style",
    "comfort",
    "curated selection",
    "purposefully curated",
    "unapologetically premium",
    "Lesotho",
    "shoes",
    "fashion",
    "accessories",
    "e-commerce",
    "online shopping",
    "Maseru",
    "sneakers",
    "boots",
    "sandals"
  ],
  author: "RIVV",
  locale: "en_LS", // English (Lesotho)
  type: "website",
  twitterCard: "summary_large_image",
  robots: "index, follow",
};

// Generate consistent page title
export function generatePageTitle(pageTitle?: string): string {
  if (!pageTitle) return baseMetadata.siteName;
  return `${pageTitle} | ${baseMetadata.siteName}`;
}

// Generate SEO-optimized metadata for pages
export function generatePageMetadata({
  title,
  description,
  keywords = [],
  path = "",
  image,
  noIndex = false,
}: {
  title?: string;
  description?: string;
  keywords?: string[];
  path?: string;
  image?: string;
  noIndex?: boolean;
}): Metadata {
  const pageTitle = generatePageTitle(title);
  const pageDescription = description || baseMetadata.description;
  const pageKeywords = [...baseMetadata.keywords, ...keywords].join(", ");
  const pageUrl = `${baseMetadata.siteUrl}${path}`;
  const pageImage = image || `${baseMetadata.siteUrl}/og-image.jpg`;

  return {
    title: pageTitle,
    description: pageDescription,
    keywords: pageKeywords,
    authors: [{ name: baseMetadata.author }],
    creator: baseMetadata.author,
    publisher: baseMetadata.author,
    robots: noIndex ? "noindex, nofollow" : baseMetadata.robots,
    
    // Open Graph
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      url: pageUrl,
      siteName: baseMetadata.siteName,
      images: [
        {
          url: pageImage,
          width: 1200,
          height: 630,
          alt: pageTitle,
        },
      ],
      locale: baseMetadata.locale,
      type: baseMetadata.type,
    },
    
    // Twitter
    twitter: {
      card: baseMetadata.twitterCard,
      title: pageTitle,
      description: pageDescription,
      images: [pageImage],
      creator: "@rivv_lesotho",
      site: "@rivv_lesotho",
    },
    
    // Additional metadata
    alternates: {
      canonical: pageUrl,
    },
    
    // Verification and other meta tags
    other: {
      "application-name": baseMetadata.siteName,
      "apple-mobile-web-app-title": baseMetadata.siteName,
      "msapplication-TileColor": "#2563eb",
      "theme-color": "#2563eb",
    },
  };
}

// Generate metadata for product pages
export function generateProductMetadata({
  productName,
  productDescription,
  productPrice,
  productImage,
  productBrand,
  categoryName,
  productId,
}: {
  productName: string;
  productDescription?: string;
  productPrice?: number;
  productImage?: string;
  productBrand?: string;
  categoryName?: string;
  productId: string;
}): Metadata {
  const title = `${productName} - ${productBrand || "Premium"} ${categoryName || "Footwear"}`;
  const description = productDescription
    ? `${productDescription.substring(0, 120)}... Purposefully curated by RIVV - quality that speaks for itself. Step in with confidence.`
    : `Discover ${productName} by ${productBrand || "premium brands"} at RIVV. Carefully selected for craftsmanship, comfort, and standout style. Quality that exceeds expectations.`;

  const keywords = [
    productName.toLowerCase(),
    productBrand?.toLowerCase(),
    categoryName?.toLowerCase(),
    "purposefully curated",
    "quality craftsmanship",
    "standout style",
    "comfort",
    "premium footwear",
    "carefully selected",
    "exceed expectations",
    "Lesotho delivery",
  ].filter(Boolean);

  return generatePageMetadata({
    title,
    description,
    keywords,
    path: `/products/${productId}`,
    image: productImage,
  });
}

// Generate metadata for order pages
export function generateOrderMetadata({
  orderNumber,
  isAdmin = false,
}: {
  orderNumber: string;
  isAdmin?: boolean;
}): Metadata {
  const title = isAdmin 
    ? `Order ${orderNumber} - Admin Management`
    : `Order ${orderNumber} - Order Details`;
  
  const description = isAdmin
    ? `Manage order ${orderNumber} - View details, update status, and process payments.`
    : `View your order ${orderNumber} details, tracking information, and delivery status.`;

  const keywords = isAdmin
    ? ["order management", "admin", "order processing", "payment verification"]
    : ["order tracking", "order details", "delivery status", "purchase history"];

  return generatePageMetadata({
    title,
    description,
    keywords,
    path: isAdmin ? `/admin/orders/${orderNumber}` : `/orders/${orderNumber}`,
    noIndex: true, // Order pages should not be indexed
  });
}

// Generate metadata for admin pages
export function generateAdminMetadata({
  pageTitle,
  pageDescription,
  path,
}: {
  pageTitle: string;
  pageDescription: string;
  path: string;
}): Metadata {
  return generatePageMetadata({
    title: `${pageTitle} - Admin`,
    description: pageDescription,
    keywords: ["admin", "management", "dashboard", "e-commerce", "administration"],
    path: `/admin${path}`,
    noIndex: true, // Admin pages should not be indexed
  });
}

// Generate structured data for products (JSON-LD)
export function generateProductStructuredData({
  productName,
  productDescription,
  productPrice,
  productImage,
  productBrand,
  categoryName,
  productId,
  availability = "InStock",
  currency = "LSL", // Lesotho Loti
}: {
  productName: string;
  productDescription?: string;
  productPrice?: number;
  productImage?: string;
  productBrand?: string;
  categoryName?: string;
  productId: string;
  availability?: "InStock" | "OutOfStock" | "PreOrder";
  currency?: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    name: productName,
    description: productDescription,
    image: productImage,
    brand: {
      "@type": "Brand",
      name: productBrand,
    },
    category: categoryName,
    sku: productId,
    offers: {
      "@type": "Offer",
      price: productPrice,
      priceCurrency: currency,
      availability: `https://schema.org/${availability}`,
      seller: {
        "@type": "Organization",
        name: baseMetadata.siteName,
      },
    },
  };
}

// Generate structured data for organization
export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: baseMetadata.siteName,
    url: baseMetadata.siteUrl,
    description: "Proudly female-founded brand delivering purposefully curated, unapologetically premium footwear. Quality that speaks for itself.",
    slogan: "Purposefully Curated. Unapologetically Premium.",
    foundingDate: "2024",
    address: {
      "@type": "PostalAddress",
      addressCountry: "LS",
      addressLocality: "Maseru",
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+266-6284-4473",
      contactType: "Customer Service",
      availableLanguage: ["English"],
    },
    founder: {
      "@type": "Person",
      gender: "Female",
    },
    sameAs: [
      // Add social media URLs when available
    ],
  };
}
